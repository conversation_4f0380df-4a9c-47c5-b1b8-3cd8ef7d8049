<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-position="top" label-width="80px" size="mini">
      <!-- 1、数量 -->
      <el-form-item prop="quantity" label="1、数量">
        <el-input
          v-model="form.quantity"
          placeholder="请输入数量"
          style="width: 200px"
          @input="handleQuantityInput"
          @keypress="handleIntegerKeypress"
        />
        <span style="margin-left: 5px;">个</span>
      </el-form-item>

      <!-- 2、是否完整 -->
      <el-form-item prop="isComplete" label="2、是否完整">
        <el-radio-group v-model="form.isComplete">
          <el-radio label="完整" border/>
          <el-radio label="不完整" border/>
        </el-radio-group>
      </el-form-item>

      <!-- 3、病理类型 -->
      <el-form-item prop="pathologyType" label="3、病理类型">
        <el-radio-group v-model="form.pathologyType" class="pathology-type-group">
          <el-radio label="腺泡腺癌" border />
          <el-radio label="导管内癌" border />
          <el-radio label="小细胞癌" border />
          <el-radio label="其他" border />
        </el-radio-group>
        <el-input
          v-if="form.pathologyType === '其他'"
          v-model="form.pathologyTypeOther"
          placeholder="请填写其他病理类型"
          maxlength="50"
          show-word-limit
          class="pathology-type-other"
        />
      </el-form-item>

      <!-- 4、分级 -->
      <el-form-item prop="grade" label="4、分级">
        <el-radio-group v-model="form.grade" class="grade-group">
          <el-radio label="ISUP 1级" border />
          <el-radio label="ISUP 2级" border />
          <el-radio label="ISUP 3级" border />
          <el-radio label="ISUP 4级" border />
          <el-radio label="ISUP 5级" border />
        </el-radio-group>

        <!-- 分级详细说明 -->
        <div v-if="form.grade" class="grade-description">
          <div v-if="form.grade === 'ISUP 1级'" class="grade-detail">
            ISUP 1级：Gleason评分≤6，仅由单个分离的、形态完好的腺体组成。
          </div>
          <div v-if="form.grade === 'ISUP 2级'" class="grade-detail">
            ISUP 2级：Gleason评分 3+4=7，主要由形态完好的腺体组成，伴有较少的形态发育不良腺体/融合腺体/筛状腺体组成。
          </div>
          <div v-if="form.grade === 'ISUP 3级'" class="grade-detail">
            ISUP 3级：Gleason评分 4+3=7，主要由发育不良的腺体/融合腺体/筛状腺体组成，伴少量形态完好的腺体。
          </div>
          <div v-if="form.grade === 'ISUP 4级'" class="grade-detail">
            ISUP 4级：Gleason评分 4+4=8、3+5=8、5+3=8，仅由发育不良的腺体/融合腺体/筛状腺体组成；或者以形态完好的腺体为主伴少量缺乏腺体分化的成分组成；或者以缺少腺体分化的成分为主伴少量形态完好的腺体组成。
          </div>
          <div v-if="form.grade === 'ISUP 5级'" class="grade-detail">
            ISUP 5级：Gleason评分 9～10，缺乏腺体形成结构（或伴坏死），伴或不伴腺体形态发育不良/融合腺体/筛状腺体。
          </div>
        </div>
      </el-form-item>

      <!-- 5、浸润深度 -->
      <el-form-item prop="invasionDepth" label="5、浸润深度（是/否浸润肌层）">
        <el-radio-group v-model="form.invasionDepth">
          <el-radio label="是" border/>
          <el-radio label="否" border/>
        </el-radio-group>
      </el-form-item>

      <!-- 包膜外侵犯 -->
      <el-form-item v-if="form.invasionDepth === '是'" prop="capsularInvasion" label="包膜外侵犯">
        <el-radio-group v-model="form.capsularInvasion">
          <el-radio label="是" border/>
          <el-radio label="否" border/>
        </el-radio-group>
      </el-form-item>

      <!-- 前列腺周围脂肪组织 -->
      <el-form-item v-if="form.invasionDepth === '是'" prop="periprostatic" label="前列腺周围脂肪组织">
        <el-radio-group v-model="form.periprostatic">
          <el-radio label="是" border/>
          <el-radio label="否" border/>
        </el-radio-group>
      </el-form-item>

      <!-- 邻近器官侵犯 -->
      <el-form-item v-if="form.invasionDepth === '是'" prop="adjacentOrganInvasion" label="邻近器官侵犯">
        <el-radio-group v-model="form.adjacentOrganInvasion">
          <el-radio label="是" border/>
          <el-radio label="否" border/>
        </el-radio-group>
      </el-form-item>

      <!-- 邻近器官侵犯详情 -->
      <el-form-item v-if="form.invasionDepth === '是' && form.adjacentOrganInvasion === '是'" prop="adjacentOrganDetails" label="邻近器官侵犯详情（可多选）">
        <el-checkbox-group v-model="form.adjacentOrganDetails">
          <el-checkbox label="精囊腺" border />
          <el-checkbox label="膀胱颈" border />
          <el-checkbox label="直肠壁" border />
          <el-checkbox label="尿道括约肌" border />
          <el-checkbox label="其他" border />
        </el-checkbox-group>
        <el-input
          v-if="form.adjacentOrganDetails.includes('其他')"
          v-model="form.adjacentOrganDetailsOther"
          placeholder="请填写其他侵犯器官"
          maxlength="50"
          show-word-limit
          class="adjacent-organ-other"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'AN0165',
  props: {
    formData: String,
  },
  components: {},
  created() {
    if (this.formData != null && this.formData != '') {
      this.form = JSON.parse(this.formData);
      // 初始化所有可能的字段
      const fields = [
        'quantity', 'isComplete', 'pathologyType', 'pathologyTypeOther', 'grade',
        'invasionDepth', 'capsularInvasion', 'periprostatic', 'adjacentOrganInvasion',
        'adjacentOrganDetails', 'adjacentOrganDetailsOther'
      ];

      fields.forEach(field => {
        if (this.form[field] === undefined) {
          if (field === 'adjacentOrganDetails') {
            this.$set(this.form, field, []);
          } else {
            this.$set(this.form, field, '');
          }
        }
      });
    }
  },
  data() {
    // 验证函数
    const validateQuantity = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入数量'));
      } else if (!/^\d+$/.test(value)) {
        callback(new Error('请输入有效的整数'));
      } else if (parseInt(value) <= 0) {
        callback(new Error('数量必须大于0'));
      } else {
        callback();
      }
    };

    const validatePathologyTypeOther = (rule, value, callback) => {
      if (this.form.pathologyType === '其他' && !value) {
        callback(new Error('请填写其他病理类型'));
      } else {
        callback();
      }
    };

    const validateCapsularInvasion = (rule, value, callback) => {
      if (this.form.invasionDepth === '是' && !value) {
        callback(new Error('请选择包膜外侵犯'));
      } else {
        callback();
      }
    };

    const validatePeriprostatic = (rule, value, callback) => {
      if (this.form.invasionDepth === '是' && !value) {
        callback(new Error('请选择前列腺周围脂肪组织'));
      } else {
        callback();
      }
    };

    const validateAdjacentOrganInvasion = (rule, value, callback) => {
      if (this.form.invasionDepth === '是' && !value) {
        callback(new Error('请选择邻近器官侵犯'));
      } else {
        callback();
      }
    };

    const validateAdjacentOrganDetails = (rule, value, callback) => {
      if (this.form.invasionDepth === '是' && this.form.adjacentOrganInvasion === '是' && (!value || value.length === 0)) {
        callback(new Error('请选择邻近器官侵犯详情'));
      } else {
        callback();
      }
    };

    const validateAdjacentOrganDetailsOther = (rule, value, callback) => {
      if (this.form.invasionDepth === '是' && this.form.adjacentOrganInvasion === '是' && this.form.adjacentOrganDetails.includes('其他') && !value) {
        callback(new Error('请填写其他侵犯器官'));
      } else {
        callback();
      }
    };

    return {
      form: {
        quantity: '',
        isComplete: undefined,
        pathologyType: '',
        pathologyTypeOther: '',
        grade: '',
        invasionDepth: undefined,
        capsularInvasion: undefined,
        periprostatic: undefined,
        adjacentOrganInvasion: undefined,
        adjacentOrganDetails: [],
        adjacentOrganDetailsOther: '',
      },
      // 表单校验
      rules: {
        quantity: [{ validator: validateQuantity, trigger: 'blur' }],
        isComplete: [{ required: true, message: '必须选择', trigger: 'blur' }],
        pathologyType: [{ required: true, message: '必须选择', trigger: 'blur' }],
        pathologyTypeOther: [{ validator: validatePathologyTypeOther, trigger: 'blur' }],
        grade: [{ required: true, message: '必须选择', trigger: 'blur' }],
        invasionDepth: [{ required: true, message: '必须选择', trigger: 'blur' }],
        capsularInvasion: [{ validator: validateCapsularInvasion, trigger: 'blur' }],
        periprostatic: [{ validator: validatePeriprostatic, trigger: 'blur' }],
        adjacentOrganInvasion: [{ validator: validateAdjacentOrganInvasion, trigger: 'blur' }],
        adjacentOrganDetails: [{ validator: validateAdjacentOrganDetails, trigger: 'change' }],
        adjacentOrganDetailsOther: [{ validator: validateAdjacentOrganDetailsOther, trigger: 'blur' }],
      },
    };
  },
  methods: {
    /** 表单值 */
    getData() {
      return this.form;
    },
    validateData() {
      let rs = true;
      this.$refs['form'].validate((valid) => {
        if (valid) {
          rs = false;
        }
      });
      return rs;
    },
    /** 处理数量输入，只允许整数 */
    handleQuantityInput(value) {
      // 只保留数字
      const numericValue = value.replace(/[^\d]/g, '');
      this.form.quantity = numericValue;
    },
    /** 处理按键事件，只允许整数 */
    handleIntegerKeypress(event) {
      const charCode = event.which ? event.which : event.keyCode;
      // 只允许数字(48-57)、退格(8)、删除(46)、方向键等控制键
      if (charCode > 31 && (charCode < 48 || charCode > 57)) {
        event.preventDefault();
      }
    },
  },
  watch: {
    'form.pathologyType'(val) {
      if (val !== '其他') {
        this.form.pathologyTypeOther = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('pathologyTypeOther');
        }
      }
    },
    'form.invasionDepth'(val) {
      if (val !== '是') {
        this.form.capsularInvasion = undefined;
        this.form.periprostatic = undefined;
        this.form.adjacentOrganInvasion = undefined;
        this.form.adjacentOrganDetails = [];
        this.form.adjacentOrganDetailsOther = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate(['capsularInvasion', 'periprostatic', 'adjacentOrganInvasion', 'adjacentOrganDetails', 'adjacentOrganDetailsOther']);
        }
      }
    },
    'form.adjacentOrganInvasion'(val) {
      if (val !== '是') {
        this.form.adjacentOrganDetails = [];
        this.form.adjacentOrganDetailsOther = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate(['adjacentOrganDetails', 'adjacentOrganDetailsOther']);
        }
      }
    },
    'form.adjacentOrganDetails'(val) {
      if (!val.includes('其他')) {
        this.form.adjacentOrganDetailsOther = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('adjacentOrganDetailsOther');
        }
      }
    },
  },
};
export const formConfig = {
  fieldList: [ {
      key:"quantity",
      label:"1、数量"
    }, {
      key:"isComplete",
      label:"2、是否完整"
    }, {
      key:"pathologyType",
      label:"3、病理类型"
    }, {
      key:"grade",
      label:"4、分级"
    }, {
      key:"invasionDepth",
      label:"5、浸润深度（是/否浸润肌层）"
    }, {
      key:"capsularInvasion",
      label:"包膜外侵犯"
    }, {
      key:"periprostatic",
      label:"前列腺周围脂肪组织"
    }, {
      key:"adjacentOrganInvasion",
      label:"邻近器官侵犯"
    } ],
};
</script>
<style scoped>
::v-deep .el-radio--mini.is-bordered {
  padding: 6px 15px 0 15px !important;
  border-radius: 3px;
  height: 28px;
}

::v-deep .el-radio__input {
  display: none !important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

::v-deep .el-radio__label {
  padding-left: 0px;
}

::v-deep .el-checkbox--mini.is-bordered {
  padding: 6px 15px 0 15px !important;
  border-radius: 3px;
  height: 28px;
  margin-right: 10px;
  margin-bottom: 10px;
}

::v-deep .el-checkbox__input {
  display: none !important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

::v-deep .el-checkbox__label {
  padding-left: 0px;
}

::v-deep .el-form-item__label {
  width: 100%;
  text-align: left;
  vertical-align: middle;
  float: left;
  color: #606266;
  line-height: 40px;
  padding: 0 12px 0 0;
  box-sizing: border-box;
  font-weight: 450;
  font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  color: #000000;
}

::v-deep .el-radio-group {
  font-size: 0;
}

::v-deep .el-checkbox-group {
  font-size: 0;
}

::v-deep .el-radio {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 10px;
}

::v-deep .el-checkbox {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 10px;
}

.pathology-type-other,
.adjacent-organ-other {
  width: 300px;
  margin-left: 10px;
  margin-top: 10px;
}

.pathology-type-group,
.grade-group {
  display: inline-flex;
  align-items: center;
  flex-wrap: wrap;
}

.grade-description {
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.grade-detail {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}
</style>
